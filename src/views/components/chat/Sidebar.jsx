import React, { useState, useRef, useEffect } from "react";
import facebook from "../../../assets/images/Chating/Facebook.svg";
import instagram from "../../../assets/images/Chating/Instragram.svg";
import flowkar from "../../../assets/images/Chating/flowkarChat.svg";
import search from "../../../assets/images/Chating/Search.svg";
import meta from "../../../assets/images/svg_icon/meta.svg";
import profile from "../../../assets/images/svg_icon/profile.svg";
import close from "../../../assets/images/Chating/Close.svg";
import Spinner from "../../../helpers/UI/Spinner";

const Sidebar = ({
  activeChannel,
  setActiveChannel,
  users,
  selectedUser: propSelectedUser,
  onUserSelect,
  iconlist,
  sloading,
  globalTypingStates,
}) => {
  const scrollContainerRef = useRef(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(false);
  const [selectedUser, setSelectedUser] = useState(propSelectedUser || null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredUsers, setFilteredUsers] = useState(users || []);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (propSelectedUser) {
      setSelectedUser(propSelectedUser);
    }
  }, [propSelectedUser]);

  useEffect(() => {
    if (users) {
      const filtered = users.filter(
        (user) =>
          user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (user.message &&
            user.message.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredUsers(filtered);
    }
  }, [searchTerm, users]);

  // Check if scrolling is needed and ensure proper scrolling behavior
  useEffect(() => {
    const checkScroll = () => {
      const container = scrollContainerRef.current;
      if (container) {
        // Calculate if arrows should be shown based on scroll position
        setShowLeftArrow(container.scrollLeft > 0);
        setShowRightArrow(
          container.scrollLeft < container.scrollWidth - container.clientWidth
        );
      }
    };

    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener("scroll", checkScroll);
      // Initial check with longer delay to ensure DOM is ready
      setTimeout(checkScroll, 200);

      // Check on resize
      window.addEventListener("resize", checkScroll);

      return () => {
        container.removeEventListener("scroll", checkScroll);
        window.removeEventListener("resize", checkScroll);
      };
    }
  }, [iconlist]); // Add iconlist as dependency to re-check when channels change

  // Scroll handling functions with fixed step size to center items
  const scrollLeft = () => {
    const container = scrollContainerRef.current;
    if (container) {
      const channelItems = container.querySelectorAll("button");
      if (channelItems.length > 0) {
        const itemWidth = channelItems[0].offsetWidth;
        container.scrollTo({
          left: container.scrollLeft - itemWidth,
          behavior: "smooth",
        });
      }
    }
  };

  const scrollRight = () => {
    const container = scrollContainerRef.current;
    if (container) {
      const channelItems = container.querySelectorAll("button");
      if (channelItems.length > 0) {
        const itemWidth = channelItems[0].offsetWidth;
        container.scrollTo({
          left: container.scrollLeft + itemWidth,
          behavior: "smooth",
        });
      }
    }
  };

  const handleUserClick = (user) => {
    // Mark all messages as read for this user immediately
    if (typeof onUserSelect === "function") {
      onUserSelect(user, { markAllRead: true });
    }
    setSelectedUser(user);
  };

  const handleChannelClick = (channel) => {
    if (activeChannel !== channel.name) {
      setIsLoading(true);
      setTimeout(() => {
        setActiveChannel(channel.name);
        setIsLoading(false);
      }, 100);
    }
  };

  // When a user is selected, mark all their messages as read and update unreadCount
  useEffect(() => {
    if (selectedUser && users && users.length > 0) {
      // If the selected user has unread messages, mark them as read
      if (selectedUser.unreadCount > 0) {
        // Call the onUserSelect callback to trigger read logic in parent if needed
        if (typeof onUserSelect === "function") {
          onUserSelect(selectedUser);
        }
      }
    }
  }, [selectedUser]);

  return (
    <>
      <style jsx>{`
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .typing-dot {
          animation: typing-bounce 1.4s infinite ease-in-out;
        }
        .typing-dot:nth-child(1) {
          animation-delay: -0.32s;
        }
        .typing-dot:nth-child(2) {
          animation-delay: -0.16s;
        }
        @keyframes typing-bounce {
          0%,
          80%,
          100% {
            transform: scale(0.8);
            opacity: 0.5;
          }
          40% {
            transform: scale(1);
            opacity: 1;
          }
        }
      `}</style>
      <div className="bg-white h-full sm:h-[84.5vh] w-full sm:w-[350px] md:w-[380px] flex flex-col p-0 sm:p-1 px-0 sm:px-3 overflow-y-auto lg:mt-[0px] rounded-none sm:rounded-lg ml-0 sm:ml-6">
        {/* Channel Selector with Improved Slider */}
        <div className="relative flex justify-center pt-6 px-8">
          {showLeftArrow && (
            <button
              onClick={scrollLeft}
              className="absolute left-0 top-[60px] transform -translate-y-1/2 z-20 bg-white shadow-md rounded-full p-1 hover:bg-gray-50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
          )}

          <div className="relative flex-1 overflow-hidden">
            <div
              ref={scrollContainerRef}
              className="flex overflow-x-auto space-x-4 scroll-smooth scrollbar-hide"
            >
              {(!iconlist && iconlist.length > 0
                ? iconlist
                : [
                    { id: "flowkar", name: "flowkar", icon: flowkar },
                    { id: "all", name: "All Meta", icon: meta },
                    { id: "facebook", name: "Facebook", icon: facebook },
                    { id: "instagram", name: "Instagram", icon: instagram },
                  ]
              ).map((channel) => (
                <button
                  key={channel.id}
                  className={`flex flex-col items-center justify-center py-1 px-2 min-w-[80px] max-w-[100px] flex-shrink-0 transition-all duration-200
        ${
          activeChannel === channel.name
            ? "scale-95 rounded-2xl mb-1 bg-gray-100 ease-in"
            : "scale-[0.8] opacity-80 hover:bg-gray-100 rounded-xl hover:scale-[0.8] ease-in"
        }`}
                  onClick={() => handleChannelClick(channel)}
                >
                  {channel.icon ? (
                    <div className="w-12 h-12 rounded-full flex items-center justify-center mb-1 overflow-hidden">
                      <img
                        src={channel.icon}
                        alt={channel.name}
                        className="w-full h-full object-contain"
                      />
                    </div>
                  ) : (
                    <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mb-1">
                      <span>{channel.name.charAt(0)}</span>
                    </div>
                  )}
                  <span className="text-xs text-center">{channel.name}</span>
                </button>
              ))}
            </div>
          </div>

          {showRightArrow && (
            <button
              onClick={scrollRight}
              className="absolute right-0 top-[60px] transform -translate-y-1/2 z-20 bg-white shadow-md rounded-full p-1 hover:bg-gray-50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          )}
        </div>
        <h2 className="text-lg sm:text-xl md:text-[24px] font-semibold ml-4 sm:ml-[15px] mb-2 mt-3 sm:mt-[16px] pt-2 font-Ubuntu">
          Messages
        </h2>
        <div className="p-2 sm:p-2 border-b border-gray-200 mb-2 mx-2 sm:mx-0">
          <div className="flex items-center bg-[#F2F0F0] p-1 rounded-[12px]">
            <img src={search} alt="Search" className="m-2 sm:m-3 h-4 w-4" />
            <input
              type="text"
              placeholder="Search"
              className="bg-transparent border-none focus:outline-none focus:ring-0 text-[#929290] font-Ubuntu font-medium w-full text-sm sm:text-base"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        <div className="flex-grow overflow-y-auto space-y-2 sm:space-y-3 px-2 sm:px-2 pb-8 sm:pb-12">
          {sloading ? (
            <div className="flex items-center justify-center h-full">
              <Spinner />
            </div>
          ) : filteredUsers && filteredUsers.length > 0 ? (
            filteredUsers.map((user) => (
              <div
                key={user.id}
                className={`relative bg-[#563D3914] cursor-pointer rounded-[12px] hover:bg-gray-100 transition px-2 p-2 w-full ${
                  selectedUser && selectedUser.id === user.id
                    ? "border border-Red bg-gray-50"
                    : ""
                }`}
                onClick={() => handleUserClick(user)}
              >
                {/* Unread indicator dot - show when user has unread messages */}
                {user.unreadCount > 0 && (
                  <div className="absolute top-1/2 right-3 -translate-y-1/2 bg-[#563D39] rounded-full w-3 h-3 shadow border-2 border-white z-10" />
                )}
                <div className="flex items-center">
                  <div className="relative">
                    {user.avatar ? (
                      <img
                        src={user.avatar}
                        alt={user.name}
                        className=" h-12 w-12 rounded-full object-cover"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = profile;
                          e.target.className =
                            "h-10 w-10 rounded-full border p-2";
                        }}
                      />
                    ) : (
                      <img
                        src={profile}
                        alt={user.name}
                        className="h-10 w-10 rounded-full border p-2"
                      />
                    )}

                    {/* {user.status === "active" && (
                      <div className="absolute bottom-0 right-0 w-2.5 h-2.5 bg-green-500 rounded-full border-2 border-white"></div>
                    )} */}
                    {user.socialPlatform && (
                      <div className="absolute bottom-0 right-0 w-5 h-5 rounded-full border-2 border-white flex items-center justify-center bg-white">
                        <img
                          src={
                            user.socialPlatform === "facebook"
                              ? facebook
                              : user.socialPlatform === "instagram"
                              ? instagram
                              : user.socialPlatform === "flowkar"
                              ? flowkar
                              : "/images/default.png"
                          }
                          alt={user.socialPlatform}
                          className="w-4 h-4"
                        />
                      </div>
                    )}
                  </div>
                  <div className="ml-3 flex-grow w-[200px] max-w-[260px]">
                    <div className="flex justify-between items-center">
                      <span className="font-semibold text-[#563D39]">
                        {user.name}
                      </span>
                    </div>
                    {/* Show typing indicator if user is typing AND not currently selected */}
                    {globalTypingStates &&
                    globalTypingStates[String(user.id)] &&
                    selectedUser?.id !== user.id ? (
                      <div className="flex items-center space-x-1">
                        <span className="text-[12px] text-[#563D39] font-medium italic">
                          typing
                        </span>
                        <div className="flex space-x-1">
                          <div className="w-1 h-1 bg-[#563D39] rounded-full typing-dot"></div>
                          <div className="w-1 h-1 bg-[#563D39] rounded-full typing-dot"></div>
                          <div className="w-1 h-1 bg-[#563D39] rounded-full typing-dot"></div>
                        </div>
                      </div>
                    ) : (
                      <p
                        className={`text-[12px] ${
                          user.unreadCount > 0
                            ? "text-[#000000] font-bold"
                            : "text-gray-500"
                        } truncate overflow-hidden whitespace-nowrap`}
                      >
                        {user.message}
                      </p>
                    )}
                    {user.post_media_url && (
                      <div className="mt-1">
                        <img
                          src={
                            user.post_media_url.startsWith("http")
                              ? user.post_media_url
                              : `https://api.flowkar.com${user.post_media_url}`
                          }
                          alt="Post Media"
                          className="rounded-md max-w-[80px] max-h-[80px]"
                          style={{ objectFit: "cover" }}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <p className="p-4 text-gray-500">No users available</p>
          )}
        </div>
      </div>
    </>
  );
};

export default Sidebar;
